import * as THREE from 'three';

export interface LightingConfig {
    timeOfDay: 'dawn' | 'day' | 'dusk' | 'night';
    intensity: number;
    enableVolumetric: boolean;
    enableShadows: boolean;
}

export default class ModernLighting {
    private scene: THREE.Scene;
    private renderer: THREE.WebGLRenderer;
    private directionalLight: THREE.DirectionalLight;
    private ambientLight: THREE.AmbientLight;
    private hemisphereLight: THREE.HemisphereLight;
    private pointLights: THREE.PointLight[] = [];
    private config: LightingConfig;

    constructor(scene: THREE.Scene, renderer: THREE.WebGLRenderer) {
        this.scene = scene;
        this.renderer = renderer;
        this.config = {
            timeOfDay: 'day',
            intensity: 1.0,
            enableVolumetric: true,
            enableShadows: true
        };
        
        this.setupRenderer();
        this.createLights();
    }

    private setupRenderer(): void {
        // Enable modern shadow mapping
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // Enable tone mapping for HDR
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.0;
        
        // Enable physically correct lighting
        this.renderer.physicallyCorrectLights = true;
    }

    private createLights(): void {
        this.createDirectionalLight();
        this.createAmbientLighting();
        this.createAccentLights();
    }

    private createDirectionalLight(): void {
        // Main sun light with warm color temperature
        this.directionalLight = new THREE.DirectionalLight(0xfff4e6, 2.5);
        this.directionalLight.position.set(50, 100, 50);
        this.directionalLight.target.position.set(0, 0, 0);
        
        // Enhanced shadow settings
        this.directionalLight.castShadow = true;
        this.directionalLight.shadow.mapSize.width = 2048;
        this.directionalLight.shadow.mapSize.height = 2048;
        this.directionalLight.shadow.camera.near = 0.5;
        this.directionalLight.shadow.camera.far = 500;
        this.directionalLight.shadow.camera.left = -100;
        this.directionalLight.shadow.camera.right = 100;
        this.directionalLight.shadow.camera.top = 100;
        this.directionalLight.shadow.camera.bottom = -100;
        this.directionalLight.shadow.bias = -0.0001;
        this.directionalLight.shadow.normalBias = 0.02;

        this.scene.add(this.directionalLight);
        this.scene.add(this.directionalLight.target);
    }

    private createAmbientLighting(): void {
        // Replace basic ambient with hemisphere light for more realistic lighting
        this.hemisphereLight = new THREE.HemisphereLight(
            0x87ceeb, // Sky color (light blue)
            0x362d1d, // Ground color (warm brown)
            0.4
        );
        this.scene.add(this.hemisphereLight);

        // Subtle ambient for fill lighting
        this.ambientLight = new THREE.AmbientLight(0x404040, 0.2);
        this.scene.add(this.ambientLight);
    }

    private createAccentLights(): void {
        // Accent lights for different portfolio areas
        const accentPositions = [
            { pos: new THREE.Vector3(-40, 15, 0), color: 0x00d4ff, intensity: 1.5 }, // Playground
            { pos: new THREE.Vector3(40, 15, 0), color: 0xed8936, intensity: 1.5 },  // Portfolio
            { pos: new THREE.Vector3(0, 15, 50), color: 0x9f7aea, intensity: 1.5 },  // Knowledge
            { pos: new THREE.Vector3(0, 25, -30), color: 0xf6ad55, intensity: 2.0 }  // Lobby
        ];

        accentPositions.forEach(({ pos, color, intensity }) => {
            const light = new THREE.PointLight(color, intensity, 50, 2);
            light.position.copy(pos);
            light.castShadow = true;
            light.shadow.mapSize.width = 512;
            light.shadow.mapSize.height = 512;
            light.shadow.camera.near = 0.5;
            light.shadow.camera.far = 50;
            
            this.pointLights.push(light);
            this.scene.add(light);
        });
    }

    public setTimeOfDay(timeOfDay: LightingConfig['timeOfDay']): void {
        this.config.timeOfDay = timeOfDay;
        
        switch (timeOfDay) {
            case 'dawn':
                this.directionalLight.color.setHex(0xffd4a3);
                this.directionalLight.intensity = 1.5;
                this.hemisphereLight.color.setHex(0xffd4a3);
                this.hemisphereLight.groundColor.setHex(0x4a3728);
                this.renderer.toneMappingExposure = 0.8;
                break;
                
            case 'day':
                this.directionalLight.color.setHex(0xfff4e6);
                this.directionalLight.intensity = 2.5;
                this.hemisphereLight.color.setHex(0x87ceeb);
                this.hemisphereLight.groundColor.setHex(0x362d1d);
                this.renderer.toneMappingExposure = 1.0;
                break;
                
            case 'dusk':
                this.directionalLight.color.setHex(0xff8c42);
                this.directionalLight.intensity = 1.0;
                this.hemisphereLight.color.setHex(0xff6b35);
                this.hemisphereLight.groundColor.setHex(0x2d1810);
                this.renderer.toneMappingExposure = 0.6;
                break;
                
            case 'night':
                this.directionalLight.color.setHex(0x4a5568);
                this.directionalLight.intensity = 0.3;
                this.hemisphereLight.color.setHex(0x1a202c);
                this.hemisphereLight.groundColor.setHex(0x0f0f0f);
                this.renderer.toneMappingExposure = 0.4;
                break;
        }
    }

    public setIntensity(intensity: number): void {
        this.config.intensity = intensity;
        this.directionalLight.intensity *= intensity;
        this.hemisphereLight.intensity *= intensity;
        this.pointLights.forEach(light => light.intensity *= intensity);
    }

    public enableShadows(enable: boolean): void {
        this.config.enableShadows = enable;
        this.directionalLight.castShadow = enable;
        this.pointLights.forEach(light => light.castShadow = enable);
        this.renderer.shadowMap.enabled = enable;
    }

    public update(deltaTime: number): void {
        // Subtle light animation for dynamic feel
        const time = Date.now() * 0.001;
        
        this.pointLights.forEach((light, index) => {
            const offset = index * Math.PI * 0.5;
            light.intensity = light.userData.baseIntensity * (1 + Math.sin(time + offset) * 0.1);
        });
    }

    public dispose(): void {
        this.scene.remove(this.directionalLight);
        this.scene.remove(this.ambientLight);
        this.scene.remove(this.hemisphereLight);
        this.pointLights.forEach(light => this.scene.remove(light));
    }
}
