/* Modern CSS Architecture for 3D Portfolio */

/* CSS Custom Properties (Variables) */
:root {
  /* Color Palette */
  --color-primary-dark: #1a365d;
  --color-primary-medium: #2d3748;
  --color-primary-light: #4a5568;
  
  --color-accent-orange: #ed8936;
  --color-accent-orange-light: #f6ad55;
  --color-accent-blue: #00d4ff;
  --color-accent-purple: #9f7aea;
  
  --color-neutral-dark: #2d3748;
  --color-neutral-medium: #718096;
  --color-neutral-light: #a0aec0;
  
  --color-surface-dark: #1a202c;
  --color-surface-medium: #2d3748;
  --color-surface-light: #4a5568;
  
  --color-interactive-hover: #00d4ff;
  --color-interactive-active: #ed8936;
  --color-interactive-disabled: #718096;
  
  /* Typography */
  --font-primary: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'Courier New', Courier, monospace;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  
  /* Glass morphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(10px);
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: var(--font-primary);
  background: linear-gradient(135deg, var(--color-surface-dark) 0%, var(--color-primary-dark) 100%);
}

/* Canvas Styles */
.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
}

#bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
}

/* Modern UI Components */

/* Glass Morphism Card */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
}

/* Modern Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--color-surface-medium);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
  transform: translateY(-2rem) scale(0.95);
  transition: transform var(--transition-normal);
}

.modal-overlay.active .modal-content {
  transform: translateY(0) scale(1);
}

/* User Counter */
.user-counter {
  position: fixed;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  z-index: 100;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-surface-dark) 0%, var(--color-primary-dark) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  transition: opacity var(--transition-slow);
}

.loading-screen.hidden {
  opacity: 0;
  pointer-events: none;
}

.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.progress-bar {
  width: 300px;
  height: 4px;
  background: var(--color-surface-light);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-accent-blue), var(--color-accent-orange));
  border-radius: var(--radius-sm);
  transition: width var(--transition-fast);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  color: var(--color-neutral-light);
  font-family: var(--font-mono);
  font-size: 0.875rem;
  text-align: center;
}

/* Mobile Joystick */
.joystick-container {
  position: fixed;
  bottom: var(--spacing-2xl);
  left: var(--spacing-2xl);
  display: none;
  z-index: 100;
}

.joystick-outer {
  width: 120px;
  height: 120px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 2px solid var(--glass-border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.joystick-inner {
  width: 40%;
  height: 40%;
  background: var(--color-accent-blue);
  border-radius: 50%;
  position: absolute;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

/* Navigation Hints */
.navigation-hints {
  position: fixed;
  bottom: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  color: white;
  font-size: 0.75rem;
  z-index: 100;
  opacity: 0.8;
  transition: opacity var(--transition-normal);
}

.navigation-hints:hover {
  opacity: 1;
}

.hint-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.hint-key {
  background: var(--color-surface-light);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-family: var(--font-mono);
  font-size: 0.625rem;
  min-width: 1.5rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .joystick-container {
    display: block;
  }
  
  .navigation-hints {
    display: none;
  }
  
  .modal-content {
    margin: var(--spacing-md);
    padding: var(--spacing-lg);
  }
  
  .joystick-outer {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .user-counter {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    font-size: 0.75rem;
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  
  .progress-bar {
    width: 250px;
  }
  
  .joystick-container {
    bottom: var(--spacing-lg);
    left: var(--spacing-lg);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .progress-fill::after {
    animation: none;
  }
}

/* Focus styles for accessibility */
button:focus,
input:focus {
  outline: 2px solid var(--color-accent-blue);
  outline-offset: 2px;
}

/* Performance Panel */
.performance-panel {
  font-family: var(--font-mono);
  font-size: 0.75rem;
}

.perf-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
  gap: var(--spacing-sm);
}

.perf-item span:first-child {
  color: var(--color-neutral-light);
}

.perf-item span:last-child {
  color: var(--color-accent-blue);
  font-weight: 600;
}

/* Loading Tips */
.loading-tips {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Online Indicator */
.online-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Notifications */
.notification {
  box-shadow: var(--shadow-lg);
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Enhanced Interactions */
.hint-key {
  transition: all var(--transition-fast);
}

.hint-item:hover .hint-key {
  background: var(--color-accent-blue);
  color: var(--color-surface-dark);
  transform: scale(1.05);
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(2rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
