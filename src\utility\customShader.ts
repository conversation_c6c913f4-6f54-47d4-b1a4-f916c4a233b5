import * as THREE from "three";
import { Vector3 } from "three";

import defaultFrag from '../../public/assets/shaders/default.frag';
import defaultVert from '../../public/assets/shaders/default.vert';

export default function customShader(color: THREE.ColorRepresentation) {
    if (!color) {
        console.log("COLOR NOT FOUND!")
        color = new THREE.Color(0xffffff); // Default to white if no color
    }

    // Ensure color is a proper Color object
    const colorObj = color instanceof THREE.Color ? color : new THREE.Color(color);

    return new THREE.ShaderMaterial({
        uniforms: {
            ...THREE.UniformsLib["common"],
            ...THREE.UniformsLib["fog"],
            ...THREE.UniformsLib["lights"],
            ...THREE.UniformsLib["bumpmap"],
            ...THREE.UniformsLib["displacementmap"],
            ...THREE.UniformsLib["normalmap"],
            diffuse: {
                value: colorObj
            },
            _opacity: {
                value: 1.0
            },
            waveRange: {
                value: 0
            },
            originPos: {
                value: new Vector3(0, 0, 0)
            },
            darkenBloom: {
                value: false // Ensure this is always false for visibility
            }
        },
        lights: true,
        transparent: true,
        vertexShader: defaultVert,
        fragmentShader: defaultFrag,
        side: THREE.FrontSide,
        depthTest: true,
        depthWrite: true
    })

}