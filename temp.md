go to [Source Code Repo](https://github.com/<PERSON><PERSON>Junias20/portofolio)

[https://<PERSON><PERSON>Junias20.github.io](https://JohansenJunias20.github.io)


###### <i>Inspired by https://bruno-simon.com</i>

<!-- COPY -->
| To do | Description   | Done    |
| :---: | ---- | :---: |
|[**Map Pointerr**](https://forums.rpgmakerweb.com/data/attachments/109/109950-e5cb7855bfce5950a9c055d7053c9d00.jpg)|draw [2D Arrow](https://forums.rpgmakerweb.com/data/attachments/109/109950-e5cb7855bfce5950a9c055d7053c9d00.jpg) on screen if character's position off the map  |
|**Spawn Wave Effect** |Objects appear from underground to the surface in sequence, start from the center point (character spawn) towards outside  | ✅ |
|[**Post Processing**](https://threejs.org/examples/#webgl_postprocessing_dof2) | [<PERSON>](https://threejs.org/examples/#webgl_postprocessing_unreal_bloom) on Billboards Image, [Blur](https://threejs.org/examples/#webgl_postprocessing_dof2) Camera's Edges, [Selective Outline](https://threejs.org/examples/#webgl_postprocessing_outline) on _knowledge_ |
|**Responsive Mobile UI**|Add Joystick & fix blury noise when screen resized|✅|
|**Desc on _knowledges_**|Whenever user click a _knowledge_, it will show it's description on html modal|
|**Player List UI**  |Draw list of players & move camera to specific player when user click player's name |✅|
|**more _playground_**  |boardgame that can be played with bots or other players like [_connect 4_](https://en.wikipedia.org/wiki/Connect_Four)  |
|**Bots to _playground_**  |implement bots so players can play alone  |
|**Advanced Multiplayer**  |implement Multiplayer to _playground_  (play connect 4 with other players)  |
|**Add more _knowledges_**  | [nginx](https://www.nginx.com/), [tailwind](https://tailwindcss.com/), [adobe](https://www.adobe.com/), [docker](https://www.docker.com/), [expo](https://expo.dev/), [aws](https://aws.amazon.com/), [laravel](https://laravel.com/), [threejs](**https**://threejs.org/), [opengl](https://en.wikipedia.org/wiki/OpenGL)|
|**Add more _billboard projects_**  | [minecraft-clone](https://github.com/JohansenJunias20/minecraft-clone), Accounting Web, [Laughing Clown](https://github.com/JohansenJunias20/laughing-clown)|
|**Contact Person**|IG, [Github](https://github.com/JohansenJunias20), and LinkedIn at *lobby* Area|
|**Add more Shadows**|shadows on movable object like character(ball), johansen mesh, and hotkeys||
|**Add Documentation**|Write readme file in every directory||
|**Dockerize Coturn**|image coturn/coturn seems to be broken, planning to make own custom coturn image||
|**Chat**|Add chat so players can communicate with each other||
<!-- |**Night Mode**|Change theme to night when || -->
