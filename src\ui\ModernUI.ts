export interface UIConfig {
    showNavigationHints: boolean;
    showPerformanceStats: boolean;
    enableAnimations: boolean;
    theme: 'dark' | 'light';
}

export class ModernUI {
    private config: UIConfig;
    private performancePanel: HTMLElement | null = null;
    private navigationHints: HTMLElement | null = null;
    private loadingScreen: HTMLElement | null = null;
    private userCounter: HTMLElement | null = null;

    constructor(config: Partial<UIConfig> = {}) {
        this.config = {
            showNavigationHints: true,
            showPerformanceStats: false,
            enableAnimations: true,
            theme: 'dark',
            ...config
        };

        this.initializeUI();
        this.setupEventListeners();
    }

    private initializeUI(): void {
        this.createPerformancePanel();
        this.updateNavigationHints();
        this.enhanceLoadingScreen();
        this.setupUserCounter();
    }

    private createPerformancePanel(): void {
        if (!this.config.showPerformanceStats) return;

        const panel = document.createElement('div');
        panel.className = 'performance-panel glass-card';
        panel.style.cssText = `
            position: fixed;
            top: 60px;
            right: 16px;
            padding: 12px;
            font-family: var(--font-mono);
            font-size: 12px;
            color: white;
            z-index: 100;
            min-width: 120px;
        `;

        panel.innerHTML = `
            <div class="perf-item">
                <span>FPS:</span>
                <span id="fps-counter">--</span>
            </div>
            <div class="perf-item">
                <span>Quality:</span>
                <span id="quality-indicator">--</span>
            </div>
            <div class="perf-item">
                <span>Triangles:</span>
                <span id="triangle-counter">--</span>
            </div>
        `;

        document.body.appendChild(panel);
        this.performancePanel = panel;
    }

    private updateNavigationHints(): void {
        this.navigationHints = document.querySelector('.navigation-hints');
        if (!this.navigationHints) return;

        // Add dynamic hints based on current area
        const hints = [
            { key: 'WASD', action: 'Move character' },
            { key: 'Mouse', action: 'Look around' },
            { key: 'Click', action: 'Interact' },
            { key: 'Z', action: 'Debug mode' }
        ];

        this.navigationHints.innerHTML = hints.map(hint => `
            <div class="hint-item">
                <span class="hint-key">${hint.key}</span>
                <span>${hint.action}</span>
            </div>
        `).join('');
    }

    private enhanceLoadingScreen(): void {
        this.loadingScreen = document.getElementById('progressModal');
        if (!this.loadingScreen) return;

        // Add loading tips
        const tips = [
            "Use WASD keys to move your character around the 3D world",
            "Click on objects to interact with them",
            "Explore different areas: Lobby, Knowledge, Portfolio, and Playground",
            "The portfolio supports multiplayer - invite friends to explore together!",
            "Graphics quality adjusts automatically based on your device performance"
        ];

        const tipsContainer = document.createElement('div');
        tipsContainer.className = 'loading-tips';
        tipsContainer.style.cssText = `
            margin-top: 24px;
            max-width: 400px;
            text-align: center;
            color: var(--color-neutral-light);
            font-size: 14px;
            line-height: 1.5;
        `;

        const randomTip = tips[Math.floor(Math.random() * tips.length)];
        tipsContainer.innerHTML = `<p>💡 ${randomTip}</p>`;

        const progressContainer = this.loadingScreen.querySelector('.progress-container');
        if (progressContainer) {
            progressContainer.appendChild(tipsContainer);
        }
    }

    private setupUserCounter(): void {
        this.userCounter = document.getElementById('board');
        if (!this.userCounter) return;

        // Add online indicator
        const indicator = document.createElement('div');
        indicator.className = 'online-indicator';
        indicator.style.cssText = `
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        `;

        this.userCounter.prepend(indicator);
    }

    private setupEventListeners(): void {
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'h' || e.key === 'H') {
                this.toggleNavigationHints();
            }
            if (e.key === 'p' && e.ctrlKey) {
                e.preventDefault();
                this.togglePerformancePanel();
            }
        });

        // Mobile detection for joystick
        if (this.isMobile()) {
            this.showMobileControls();
        }

        // Window resize handling
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    public updatePerformanceStats(fps: number, quality: string, triangles: number): void {
        if (!this.performancePanel) return;

        const fpsCounter = this.performancePanel.querySelector('#fps-counter');
        const qualityIndicator = this.performancePanel.querySelector('#quality-indicator');
        const triangleCounter = this.performancePanel.querySelector('#triangle-counter');

        if (fpsCounter) fpsCounter.textContent = fps.toString();
        if (qualityIndicator) qualityIndicator.textContent = quality;
        if (triangleCounter) triangleCounter.textContent = this.formatNumber(triangles);
    }

    public updateUserCount(count: number): void {
        if (!this.userCounter) return;

        const span = this.userCounter.querySelector('span');
        if (span) {
            span.textContent = `${count} user${count !== 1 ? 's' : ''}`;
        }
    }

    public updateLoadingProgress(progress: number, text: string): void {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
        if (progressText) {
            progressText.textContent = text;
        }
    }

    public hideLoadingScreen(): void {
        if (this.loadingScreen) {
            this.loadingScreen.classList.add('hidden');
            setTimeout(() => {
                if (this.loadingScreen) {
                    this.loadingScreen.style.display = 'none';
                }
            }, 500);
        }
    }

    public showNotification(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            background: var(--glass-bg);
            backdrop-filter: var(--glass-backdrop);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
            color: white;
            font-size: 14px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-normal);
            max-width: 300px;
        `;

        const colors = {
            info: '#00d4ff',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444'
        };

        notification.style.borderLeftColor = colors[type];
        notification.style.borderLeftWidth = '4px';
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 4000);
    }

    public updateAreaHints(area: 'lobby' | 'knowledge' | 'portfolio' | 'playground'): void {
        if (!this.navigationHints) return;

        const areaHints = {
            lobby: [
                { key: 'WASD', action: 'Explore the lobby' },
                { key: 'Click', action: 'View contact info' }
            ],
            knowledge: [
                { key: 'WASD', action: 'Browse technologies' },
                { key: 'Click', action: 'Learn more' }
            ],
            portfolio: [
                { key: 'WASD', action: 'View projects' },
                { key: 'Click', action: 'Open project details' }
            ],
            playground: [
                { key: 'WASD', action: 'Try interactive demos' },
                { key: 'Click', action: 'Play games' }
            ]
        };

        const hints = areaHints[area] || areaHints.lobby;
        this.navigationHints.innerHTML = hints.map(hint => `
            <div class="hint-item">
                <span class="hint-key">${hint.key}</span>
                <span>${hint.action}</span>
            </div>
        `).join('');
    }

    private toggleNavigationHints(): void {
        if (!this.navigationHints) return;
        
        this.config.showNavigationHints = !this.config.showNavigationHints;
        this.navigationHints.style.display = this.config.showNavigationHints ? 'block' : 'none';
    }

    private togglePerformancePanel(): void {
        this.config.showPerformanceStats = !this.config.showPerformanceStats;
        
        if (this.config.showPerformanceStats && !this.performancePanel) {
            this.createPerformancePanel();
        } else if (this.performancePanel) {
            this.performancePanel.style.display = this.config.showPerformanceStats ? 'block' : 'none';
        }
    }

    private showMobileControls(): void {
        const joystickContainer = document.getElementById('container_joystick');
        if (joystickContainer) {
            joystickContainer.style.display = 'block';
        }

        // Hide navigation hints on mobile
        if (this.navigationHints) {
            this.navigationHints.style.display = 'none';
        }
    }

    private handleResize(): void {
        // Update mobile controls visibility
        if (this.isMobile()) {
            this.showMobileControls();
        } else {
            const joystickContainer = document.getElementById('container_joystick');
            if (joystickContainer) {
                joystickContainer.style.display = 'none';
            }
            if (this.navigationHints && this.config.showNavigationHints) {
                this.navigationHints.style.display = 'block';
            }
        }
    }

    private isMobile(): boolean {
        return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    private formatNumber(num: number): string {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    public dispose(): void {
        if (this.performancePanel) {
            document.body.removeChild(this.performancePanel);
        }
    }
}
