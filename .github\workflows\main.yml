name: Pulling new Source Code to deploy-server

on:
  push:
    branches: [master]

jobs:
  build_deploy:
    runs-on: [self-hosted]
    strategy:
      matrix:
        node-version: [14.x]
    steps:
      # - uses: actions/checkout@v2
      - name: Checkout
        uses: actions/checkout@v2
      - name: set refresh token spotify API to .txt file
        run: rm -f refreshtoken.spotify.txt; echo "${{ secrets.refresh_token_spotify }}" >> refreshtoken.spotify.txt;
      - name: Build Files
        run: bash ./build.sh -m PROD --use-docker
        continue-on-error: true
      - name: Clone deploy repo (johansenjunias20.github.io)
        run: cd ../;rm -r -f ./JohansenJunias20.github.io/;ls;git clone https://JohansenJunias20:${{ secrets.PASSWORD }}@github.com/JohansenJunias20/JohansenJunias20.github.io.git
      - name: Copy build files to deploy directory
        run: ls; cp -r -f public/* ../<PERSON>senJunias20.github.io/
      - name: Copy Readme files Section
        run: bash copy_readme.sh
      - name: Set up identity before push to deploy server
        run: git config --global user.email "<EMAIL>"; git config --global user.name "JohansenJunias20"
      - name: Debug
        run: cd ../JohansenJunias20.github.io/; cat readme.md;
      - name: Push to Deploy Repository
        run: cd ../JohansenJunias20.github.io/; git update-index --no-assume-unchanged readme.md;git add .; git commit -m "${{ github.event.head_commit.message }}";git push https://JohansenJunias20:${{ secrets.PASSWORD }}@github.com/JohansenJunias20/JohansenJunias20.github.io.git;
        continue-on-error: true # incase jika tidak ada changes pada front end code
        # run server
      - name: SSL renew
        run: bash ./ssl_renew.sh
      - name: Run Server to Self Hosted Github Runner as Service
        run: bash ./install.sh
      - name: Wait docker
        run: sleep 30
