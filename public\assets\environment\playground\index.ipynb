{"cells": [{"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hello world\n"]}], "source": ["print(\"hello world\")\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41995</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41996</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41997</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41998</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41999</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>42000 rows × 10 columns</p>\n", "</div>"], "text/plain": ["       0  1  2  3  4  5  6  7  8  9\n", "0      0  1  0  0  0  0  0  0  0  0\n", "1      1  0  0  0  0  0  0  0  0  0\n", "2      0  1  0  0  0  0  0  0  0  0\n", "3      0  0  0  0  1  0  0  0  0  0\n", "4      1  0  0  0  0  0  0  0  0  0\n", "...   .. .. .. .. .. .. .. .. .. ..\n", "41995  1  0  0  0  0  0  0  0  0  0\n", "41996  0  1  0  0  0  0  0  0  0  0\n", "41997  0  0  0  0  0  0  0  1  0  0\n", "41998  0  0  0  0  0  0  1  0  0  0\n", "41999  0  0  0  0  0  0  0  0  0  1\n", "\n", "[42000 rows x 10 columns]"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import cv2\n", "from PIL import Image\n", "train = pd.read_csv(\"./images/train.csv\")\n", "X_train = train.drop(columns=['label'])\n", "temp = np.array(X_train)\n", "temp = temp.reshape(-1,28,28)\n", "temp = Image.fromarray(temp[0].astype(np.uint8))\n", "temp.save(\"test.png\")\n", "# cv2.imwrite(\"./test.png\",temp)\n", "exit\n", "\n", "from sklearn.preprocessing import MinMaxScaler\n", "scaler = MinMaxScaler()\n", "X_train = scaler.fit_transform(X_train)\n", "\n", "Y_train = train['label']\n", "Y_train = pd.get_dummies(train['label'], columns=['label'])\n", "Y_train"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "X_train, X_validation, Y_train, Y_validation = train_test_split(X_train,Y_train,test_size=0.1)"]}, {"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "X_train = np.array(X_train)\n", "Y_train = np.array(Y_train)"]}, {"cell_type": "code", "execution_count": 113, "metadata": {}, "outputs": [{"data": {"text/plain": ["(37800, 28, 28, 1)"]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train = X_train.reshape((-1,28,28,1))\n", "X_train.shape"]}, {"cell_type": "code", "execution_count": 114, "metadata": {}, "outputs": [{"data": {"text/plain": ["10"]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}], "source": ["len(Y_train[0])"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"sequential_9\"\n", "_________________________________________________________________\n", " Layer (type)                Output Shape              Param #   \n", "=================================================================\n", " conv2d_19 (Conv2D)          (None, 26, 26, 16)        160       \n", "                                                                 \n", " max_pooling2d_14 (<PERSON><PERSON><PERSON><PERSON>  (None, 13, 13, 16)       0         \n", " g2D)                                                            \n", "                                                                 \n", " conv2d_20 (Conv2D)          (None, 11, 11, 32)        4640      \n", "                                                                 \n", " flatten_9 (<PERSON><PERSON>)         (None, 3872)              0         \n", "                                                                 \n", " dense_18 (<PERSON><PERSON>)            (None, 32)                123936    \n", "                                                                 \n", " dense_19 (<PERSON><PERSON>)            (None, 10)                330       \n", "                                                                 \n", "=================================================================\n", "Total params: 129,066\n", "Trainable params: 129,066\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["import tensorflow as tf\n", "\n", "from tensorflow.keras import datasets, layers, models\n", "import matplotlib.pyplot as plt\n", "\n", "model = models.Sequential()\n", "model.add(layers.Conv2D(16, (3, 3), activation='relu', input_shape=(28, 28, 1)))\n", "model.add(layers.MaxPooling2D((2, 2)))\n", "model.add(layers.Conv2D(32, (3, 3), activation='relu'))\n", "model.add(layers.MaxPooling2D((2, 2)))\n", "model.add(layers.<PERSON><PERSON>())\n", "model.add(layers.Dense(32, activation='relu'))\n", "model.add(layers.Dense(10, activation='softmax'))\n", "model.summary()"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/5\n", "1182/1182 [==============================] - 6s 4ms/step - loss: 0.2128 - accuracy: 0.9370\n", "Epoch 2/5\n", "1182/1182 [==============================] - 5s 4ms/step - loss: 0.0707 - accuracy: 0.9781\n", "Epoch 3/5\n", "1182/1182 [==============================] - 5s 4ms/step - loss: 0.0495 - accuracy: 0.9840\n", "Epoch 4/5\n", "1182/1182 [==============================] - 5s 5ms/step - loss: 0.0359 - accuracy: 0.9889\n", "Epoch 5/5\n", "1182/1182 [==============================] - 5s 4ms/step - loss: 0.0260 - accuracy: 0.9914\n"]}, {"data": {"text/plain": ["<keras.callbacks.History at 0x1e18852e370>"]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}], "source": ["import tensorflow as tf\n", "model.compile(optimizer=\"adam\",loss=\"categorical_crossentropy\", metrics=['accuracy'])\n", "model.fit(x=X_train, y=Y_train, epochs=10)"]}, {"cell_type": "code", "execution_count": 117, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[5, 3, 6, 6, 2, 3, 2, 8, 4, 5, 0, 3, 1, 5, 4, 2, 8, 3, 6, 7]\n", "[5, 3, 6, 6, 2, 3, 2, 8, 4, 3, 0, 3, 1, 5, 4, 2, 8, 3, 6, 7]\n"]}], "source": ["X_validation = np.array(X_validation).reshape(-1,28,28,1)\n", "result = model.predict(x=X_validation)\n", "import typing\n", "def getResult(raw_result:list):\n", "    max_val = -1\n", "    max_index = 0\n", "    for index, val in enumerate(raw_result):\n", "        if val > max_val:\n", "            max_index = index\n", "            max_val = val\n", "    return max_index\n", "\n", "result = list(map(lambda x: getResult(x),result))\n", "print(result[0:20])\n", "Y_validation = list(map(lambda x: getResult(x),np.array(Y_validation)))\n", "print(Y_validation[0:20])"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(784, 1)\n", "(1, 28, 28, 1)\n"]}, {"data": {"text/plain": ["5"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["import cv2\n", "img = cv2.imread('./4_test.png',cv2.IMREAD_GRAYSCALE)\n", "output = cv2.resize(img, (28,28))\n", "img = np.array(output)\n", "img = np.array(list(map(lambda x: 255 - x, img.reshape(-1,1))))\n", "print(img.shape)\n", "img = np.array(img).reshape(28,28,1)\n", "cv2.imwrite(\"./out.png\",img)\n", "scaler_2 = MinMaxScaler()\n", "img = scaler_2.fit_transform(img.reshape(-1,1))\n", "img = np.array(img).reshape(1,28,28,1)\n", "print(img.shape)\n", "result_8 = model.predict(img)\n", "getResult(result_8[0])\n", "# image warnanya belum di inverse sbelum dipredict "]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.13.0\n"]}], "source": ["# model.save(\"model.h5\")\n", "import tensorflowjs as tfjs\n", "tfjs.converters.save_keras_model(model, \"public/\")\n", "print(tfjs.__version__)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 2}