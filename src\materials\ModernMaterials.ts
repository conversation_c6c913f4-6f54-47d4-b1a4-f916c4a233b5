import * as THREE from 'three';

export interface MaterialConfig {
    baseColor: THREE.ColorRepresentation;
    metalness: number;
    roughness: number;
    emissive?: THREE.ColorRepresentation;
    emissiveIntensity?: number;
    normalScale?: number;
    envMapIntensity?: number;
}

export class ModernMaterials {
    private static textureLoader = new THREE.TextureLoader();
    private static cubeTextureLoader = new THREE.CubeTextureLoader();
    private static envMap: THREE.CubeTexture | null = null;

    // Modern color palette
    public static readonly COLORS = {
        // Primary colors
        PRIMARY_DARK: 0x1a365d,
        PRIMARY_MEDIUM: 0x2d3748,
        PRIMARY_LIGHT: 0x4a5568,
        
        // Accent colors
        ACCENT_ORANGE: 0xed8936,
        ACCENT_ORANGE_LIGHT: 0xf6ad55,
        ACCENT_BLUE: 0x00d4ff,
        ACCENT_PURPLE: 0x9f7aea,
        
        // Neutral colors
        NEUTRAL_DARK: 0x2d3748,
        NEUTRAL_MEDIUM: 0x718096,
        NEUTRAL_LIGHT: 0xa0aec0,
        
        // Surface colors
        SURFACE_DARK: 0x1a202c,
        SURFACE_MEDIUM: 0x2d3748,
        SURFACE_LIGHT: 0x4a5568,
        
        // Interactive colors
        INTERACTIVE_HOVER: 0x00d4ff,
        INTERACTIVE_ACTIVE: 0xed8936,
        INTERACTIVE_DISABLED: 0x718096
    };

    public static async initializeEnvironment(): Promise<void> {
        // Create a simple environment map for reflections
        const pmremGenerator = new THREE.PMREMGenerator(new THREE.WebGLRenderer());
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x87ceeb);

        // Add some basic lighting to the environment
        const ambientLight = new THREE.AmbientLight(0x87ceeb, 0.4);
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1);
        scene.add(ambientLight);
        scene.add(directionalLight);

        const envTexture = pmremGenerator.fromScene(scene);
        this.envMap = envTexture.texture as THREE.CubeTexture;
        pmremGenerator.dispose();
    }

    public static createPBRMaterial(config: MaterialConfig): THREE.MeshStandardMaterial {
        const material = new THREE.MeshStandardMaterial({
            color: config.baseColor,
            metalness: config.metalness,
            roughness: config.roughness,
            envMap: this.envMap,
            envMapIntensity: config.envMapIntensity || 1.0
        });

        if (config.emissive) {
            material.emissive = new THREE.Color(config.emissive);
            material.emissiveIntensity = config.emissiveIntensity || 0.1;
        }

        return material;
    }

    // Predefined materials for different portfolio elements
    public static createFloorMaterial(): THREE.MeshStandardMaterial {
        return this.createPBRMaterial({
            baseColor: this.COLORS.SURFACE_DARK,
            metalness: 0.1,
            roughness: 0.8,
            envMapIntensity: 0.3
        });
    }

    public static createCharacterMaterial(): THREE.MeshStandardMaterial {
        return this.createPBRMaterial({
            baseColor: this.COLORS.ACCENT_BLUE,
            metalness: 0.2,
            roughness: 0.4,
            emissive: this.COLORS.ACCENT_BLUE,
            emissiveIntensity: 0.05,
            envMapIntensity: 0.8
        });
    }

    public static createNavigationMaterial(): THREE.MeshStandardMaterial {
        return this.createPBRMaterial({
            baseColor: this.COLORS.PRIMARY_MEDIUM,
            metalness: 0.3,
            roughness: 0.6,
            envMapIntensity: 0.5
        });
    }

    public static createInteractiveMaterial(): THREE.MeshStandardMaterial {
        return this.createPBRMaterial({
            baseColor: this.COLORS.ACCENT_ORANGE,
            metalness: 0.1,
            roughness: 0.3,
            emissive: this.COLORS.ACCENT_ORANGE,
            emissiveIntensity: 0.1,
            envMapIntensity: 0.7
        });
    }

    public static createKnowledgeMaterial(): THREE.MeshStandardMaterial {
        return this.createPBRMaterial({
            baseColor: this.COLORS.ACCENT_PURPLE,
            metalness: 0.2,
            roughness: 0.5,
            envMapIntensity: 0.6
        });
    }

    public static createProjectMaterial(): THREE.MeshStandardMaterial {
        return this.createPBRMaterial({
            baseColor: this.COLORS.ACCENT_ORANGE_LIGHT,
            metalness: 0.4,
            roughness: 0.3,
            envMapIntensity: 0.8
        });
    }

    public static createPlaygroundMaterial(): THREE.MeshStandardMaterial {
        return this.createPBRMaterial({
            baseColor: this.COLORS.ACCENT_BLUE,
            metalness: 0.6,
            roughness: 0.2,
            envMapIntensity: 1.0
        });
    }

    // Enhanced shader material with modern features
    public static createEnhancedShaderMaterial(
        vertexShader: string,
        fragmentShader: string,
        uniforms: { [key: string]: THREE.IUniform } = {}
    ): THREE.ShaderMaterial {
        const defaultUniforms = {
            time: { value: 0.0 },
            resolution: { value: new THREE.Vector2(window.innerWidth, window.innerHeight) },
            envMap: { value: this.envMap },
            ...uniforms
        };

        return new THREE.ShaderMaterial({
            vertexShader,
            fragmentShader,
            uniforms: defaultUniforms,
            transparent: true,
            side: THREE.DoubleSide
        });
    }

    // Animated material for special effects
    public static createAnimatedMaterial(baseConfig: MaterialConfig): THREE.MeshStandardMaterial {
        const material = this.createPBRMaterial(baseConfig);
        
        // Add animation properties
        (material as any).userData = {
            isAnimated: true,
            animationSpeed: 1.0,
            baseEmissiveIntensity: material.emissiveIntensity || 0.0
        };

        return material;
    }

    // Update animated materials
    public static updateAnimatedMaterials(materials: THREE.Material[], deltaTime: number): void {
        const time = Date.now() * 0.001;

        materials.forEach(material => {
            if ((material as any).userData?.isAnimated && material instanceof THREE.MeshStandardMaterial) {
                const userData = (material as any).userData;
                const pulse = Math.sin(time * userData.animationSpeed) * 0.5 + 0.5;
                material.emissiveIntensity = userData.baseEmissiveIntensity * (1 + pulse * 0.3);
            }
        });
    }

    // Hover effect for interactive materials
    public static createHoverEffect(material: THREE.MeshStandardMaterial): {
        onHover: () => void;
        onLeave: () => void;
    } {
        const originalColor = material.color.clone();
        const originalEmissiveIntensity = material.emissiveIntensity;

        return {
            onHover: () => {
                material.color.lerp(new THREE.Color(this.COLORS.INTERACTIVE_HOVER), 0.3);
                material.emissiveIntensity = Math.min(originalEmissiveIntensity * 2, 0.5);
            },
            onLeave: () => {
                material.color.copy(originalColor);
                material.emissiveIntensity = originalEmissiveIntensity;
            }
        };
    }

    // Dispose of resources
    public static dispose(): void {
        if (this.envMap) {
            this.envMap.dispose();
            this.envMap = null;
        }
    }
}
