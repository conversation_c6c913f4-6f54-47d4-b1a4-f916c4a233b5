ws-server is a directory for websocket server (ws stands for WebSocket)
this used as a server for:  
- Signalling SDP and candidate between peers to establish a connection
- Indexing List of Players
### .env
- This file is auto generated by prod.sh or dev.sh **do not change content of this file manually**.  
- This is the copied version of `.env` file in `../.env` with additional variable PRODUCTION=TRUE or not, it depens what script run dev.sh or prod.sh  
- Variables accessed by `index.js`
  
### dev.sh
- Run WebSocket Server with nodemon
- Called by `../dev.sh`

### index.js
- Javascript Code for WS server

### prod.sh
- Run WebSocket Server with node