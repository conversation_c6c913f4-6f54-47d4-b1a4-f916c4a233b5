# DO NOT EDIT THIS FILE MANUALLY.
listening-port=3478
# and 5349 for TLS (secure)
tls-listening-port=5349

# Require authentication
fingerprint
lt-cred-mech

realm=portofolio.orbitskomputer.com
# if is your first time configuring, just use the domain as name
server-name=portofolio.orbitskomputer.com

# Important: 
user=guest:welost123
min-port=49152
max-port=49200
total-quota=100
stale-nonce=600

# Path to the SSL certificate and private key. In this example we will use
# the letsencrypt generated certificate files.
cert=/etc/letsencrypt/live/portofolio.orbitskomputer.com/cert.pem
pkey=/etc/letsencrypt/live/portofolio.orbitskomputer.com/privkey.pem

# Specify the allowed OpenSSL cipher list for TLS/DTLS connections
# cipher-list="ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384"

