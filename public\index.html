<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta itemprop="image" content="https://johansenjunias20.github.io/desc.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Johansen Junias - Student">
    <meta name="twitter:image" content="https://johansenjunias20.github.io/desc.png">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Johansen Junias 3D Portfolio">
    <meta property="og:title" content="<PERSON><PERSON> Jun<PERSON>'s Portfolio">
    <meta property="og:description"
        content="Experience my 3D Portfolio Website and support multiplayer Peer-to-Peer. Built-in Websocket, WebRTC, and WebGL">
    <meta property="og:description"
        content="Experience Johansen Junias 3D Portfolio Website and support multiplayer Peer-to-Peer. Built-in Websocket, WebRTC, and WebGL">
    <meta itemprop="description"
        content="Experience Johansen Junias 3D Portfolio Website and support multiplayer Peer-to-Peer. Built-in Websocket, WebRTC, and WebGL">
    <meta property="og:image" content="https://johansenjunias20.github.io/desc.png">
    <title>Johansen Junias 3D Portfolio</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <meta property="og:keywords"
        content="Johansen Junias Sutanto,Johansen Junias,Johansen,Bruno Simon,Three.js,WebGL,WebRTC,Websocket,docker,3D portfolio,portfolio,Web Programming Unpas">
    <meta property="keywords"
        content="Johansen Junias Sutanto,Johansen Junias,Johansen,Bruno Simon,Three.js,WebGL,WebRTC,Websocket,docker,3D portfolio,portfolio,Web Programming Unpas">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800;900&display=swap"
        rel="stylesheet">

    <!-- Modern CSS Architecture -->
    <link rel="stylesheet" href="/src/styles/modern.css">

</head>

<body>
    <div class="canvas-container">
        <canvas id="bg"></canvas>
        <canvas id="debug" style="position: absolute; z-index: -1; display: none; width: 900px; height: 900px;" width="900" height="900"></canvas>
    </div>

    <script>
        var blur = false;
        //yg onblur sudah ada di index.ts
        document.onfocus = async (e) => {
            blur = false;
            // var audio = document.querySelector("#sound");
            // audio.volume = 0.2;
        }
        document.onmousedown = async (e) => {
            // var audio = document.querySelector("#sound");
            console.log("audio play")
            // await audio.play();
            // audio.volume = 0.2;
        }
        function setVolume() {
            // var audio = document.getElementById("sound");
            // console.log("audio setted")
            // audio.volume = 0.2;
        }
    </script>
    <!-- <audio loop onloadeddata="setVolume()" style="display: none;" id="sound" autoplay controls>
        <source src="https://d1uy6sgqjgxsyy.cloudfront.net/shared/xiaomi-ambient-music_final.mp3" type="audio/mpeg">
    </audio> -->

    <!-- Nicknames overlay -->
    <div id="nicknames" style="pointer-events: none;"></div>

    <!-- Modern Modal -->
    <div id="modal" class="modal-overlay">
        <div id="modal_content" class="modal-content">
            <h1>test</h1>
        </div>
    </div>

    <!-- User Counter -->
    <div id="board" class="user-counter">
        <span>0 users</span>
    </div>
    <!-- Mobile Joystick -->
    <div id="container_joystick" class="joystick-container">
        <div id="outer_joystick" class="joystick-outer">
            <div id="inner_joystick" class="joystick-inner"></div>
        </div>
    </div>
    <!-- Navigation Hints -->
    <div class="navigation-hints">
        <div class="hint-item">
            <span class="hint-key">WASD</span>
            <span>Move character</span>
        </div>
        <div class="hint-item">
            <span class="hint-key">Mouse</span>
            <span>Look around</span>
        </div>
        <div class="hint-item">
            <span class="hint-key">Click</span>
            <span>Interact</span>
        </div>
    </div>

    <script defer>
        function testfunc() {
            alert("worked")
        }

        // Modern modal handling
        document.querySelector("#modal").onclick = (e) => {
            if (durationModal) return;
            if (e.target == e.currentTarget) {
                hideModal()
            }
        }

        var durationModal = 0;
        function showModal(duration) {
            durationModal = duration;
            const modal = document.querySelector("#modal");
            modal.classList.add("active");

            console.log({ durationModal })
            if (durationModal) //not zero
                setTimeout(() => {
                    hideModal();
                }, durationModal * 1000);
        }

        function hideModal() {
            const modal = document.querySelector("#modal");
            modal.classList.remove("active");
        }
    </script>

    <!-- Modern Loading Screen -->
    <div id="progressModal" class="loading-screen">
        <div class="progress-container">
            <div class="progress-bar">
                <div id="progressBar" class="progress-fill" style="width: 0%;"></div>
            </div>
            <div id="progressText" class="progress-text">Loading 3D Assets...</div>
        </div>
    </div>
    <script defer src="/dist/bundle.js"></script>
</body>

</html>