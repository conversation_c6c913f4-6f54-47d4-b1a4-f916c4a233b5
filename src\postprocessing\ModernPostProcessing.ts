import * as THREE from 'three';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass';
import { SSAARenderPass } from 'three/examples/jsm/postprocessing/SSAARenderPass';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass';
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader';
import { ColorCorrectionShader } from 'three/examples/jsm/shaders/ColorCorrectionShader';
import { FilmShader } from 'three/examples/jsm/shaders/FilmShader';

export interface PostProcessingConfig {
    enableBloom: boolean;
    enableSSAA: boolean;
    enableFXAA: boolean;
    enableColorCorrection: boolean;
    enableFilmGrain: boolean;
    bloomStrength: number;
    bloomRadius: number;
    bloomThreshold: number;
}

export default class ModernPostProcessing {
    private renderer: THREE.WebGLRenderer;
    private scene: THREE.Scene;
    private camera: THREE.PerspectiveCamera;
    private composer: EffectComposer;
    private bloomComposer: EffectComposer;
    private renderPass: RenderPass;
    private bloomPass: UnrealBloomPass;
    private ssaaPass: SSAARenderPass;
    private fxaaPass: ShaderPass;
    private outlinePass: OutlinePass;
    private colorCorrectionPass: ShaderPass;
    private filmPass: ShaderPass;
    private finalPass: ShaderPass;
    private config: PostProcessingConfig;

    constructor(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.PerspectiveCamera) {
        this.renderer = renderer;
        this.scene = scene;
        this.camera = camera;
        
        this.config = {
            enableBloom: true,
            enableSSAA: true,
            enableFXAA: true,
            enableColorCorrection: true,
            enableFilmGrain: false,
            bloomStrength: 0.3,
            bloomRadius: 0.1,
            bloomThreshold: 0.85
        };

        this.initializePasses();
        this.setupComposer();
    }

    private initializePasses(): void {
        // Basic render pass
        this.renderPass = new RenderPass(this.scene, this.camera);

        // Bloom pass for glowing effects
        this.bloomPass = new UnrealBloomPass(
            new THREE.Vector2(window.innerWidth, window.innerHeight),
            this.config.bloomStrength,
            this.config.bloomRadius,
            this.config.bloomThreshold
        );

        // SSAA for high-quality anti-aliasing
        this.ssaaPass = new SSAARenderPass(this.scene, this.camera, new THREE.Color(0x000000), 1.0);
        this.ssaaPass.sampleLevel = 2;
        this.ssaaPass.unbiased = true;

        // FXAA for performance-friendly anti-aliasing
        this.fxaaPass = new ShaderPass(FXAAShader);
        this.updateFXAAResolution();

        // Outline pass for interactive elements
        this.outlinePass = new OutlinePass(
            new THREE.Vector2(window.innerWidth, window.innerHeight),
            this.scene,
            this.camera
        );
        this.outlinePass.edgeStrength = 3.0;
        this.outlinePass.edgeGlow = 0.5;
        this.outlinePass.edgeThickness = 2.0;
        this.outlinePass.visibleEdgeColor = new THREE.Color(0x00d4ff);
        this.outlinePass.hiddenEdgeColor = new THREE.Color(0x00d4ff);

        // Color correction for cinematic look
        this.colorCorrectionPass = new ShaderPass(ColorCorrectionShader);
        this.colorCorrectionPass.uniforms['powRGB'].value = new THREE.Vector3(1.2, 1.1, 1.0);
        this.colorCorrectionPass.uniforms['mulRGB'].value = new THREE.Vector3(1.0, 1.05, 1.1);

        // Film grain for texture
        this.filmPass = new ShaderPass(FilmShader);
        this.filmPass.uniforms['nIntensity'].value = 0.1;
        this.filmPass.uniforms['sIntensity'].value = 0.05;
        this.filmPass.uniforms['sCount'].value = 1024;
        this.filmPass.uniforms['grayscale'].value = 0;

        // Final composition pass
        this.createFinalPass();
    }

    private createFinalPass(): void {
        const finalShader = {
            uniforms: {
                baseTexture: { value: null as THREE.Texture | null },
                bloomTexture: { value: null as THREE.Texture | null },
                bloomStrength: { value: this.config.bloomStrength },
                exposure: { value: 1.0 },
                gamma: { value: 2.2 }
            },
            vertexShader: `
                varying vec2 vUv;
                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform sampler2D baseTexture;
                uniform sampler2D bloomTexture;
                uniform float bloomStrength;
                uniform float exposure;
                uniform float gamma;
                
                varying vec2 vUv;
                
                // ACES tone mapping
                vec3 ACESFilm(vec3 x) {
                    float a = 2.51;
                    float b = 0.03;
                    float c = 2.43;
                    float d = 0.59;
                    float e = 0.14;
                    return clamp((x * (a * x + b)) / (x * (c * x + d) + e), 0.0, 1.0);
                }
                
                void main() {
                    vec4 base = texture2D(baseTexture, vUv);
                    vec4 bloom = texture2D(bloomTexture, vUv);
                    
                    // Combine base and bloom
                    vec3 color = base.rgb + bloom.rgb * bloomStrength;
                    
                    // Apply exposure
                    color *= exposure;
                    
                    // Apply ACES tone mapping
                    color = ACESFilm(color);
                    
                    // Apply gamma correction
                    color = pow(color, vec3(1.0 / gamma));
                    
                    gl_FragColor = vec4(color, base.a);
                }
            `
        };

        this.finalPass = new ShaderPass(new THREE.ShaderMaterial(finalShader), 'baseTexture');
        this.finalPass.needsSwap = true;
    }

    private setupComposer(): void {
        // Main composer
        this.composer = new EffectComposer(this.renderer);
        this.composer.setSize(window.innerWidth, window.innerHeight);
        this.composer.setPixelRatio(window.devicePixelRatio);

        // Bloom composer (separate for selective bloom)
        this.bloomComposer = new EffectComposer(this.renderer);
        this.bloomComposer.setSize(window.innerWidth, window.innerHeight);
        this.bloomComposer.setPixelRatio(window.devicePixelRatio);
        this.bloomComposer.renderToScreen = false;

        this.updateComposerPasses();
    }

    private updateComposerPasses(): void {
        // Clear existing passes
        this.composer.passes = [];
        this.bloomComposer.passes = [];

        // Add render pass
        this.composer.addPass(this.renderPass);

        // Add SSAA if enabled and high quality
        if (this.config.enableSSAA) {
            this.composer.addPass(this.ssaaPass);
        }

        // Setup bloom composer
        if (this.config.enableBloom) {
            this.bloomComposer.addPass(this.renderPass);
            this.bloomComposer.addPass(this.bloomPass);
            
            // Connect bloom texture to final pass
            this.finalPass.uniforms['bloomTexture'].value = this.bloomComposer.renderTarget2.texture;
            this.composer.addPass(this.finalPass);
        }

        // Add outline pass
        this.composer.addPass(this.outlinePass);

        // Add color correction
        if (this.config.enableColorCorrection) {
            this.composer.addPass(this.colorCorrectionPass);
        }

        // Add FXAA if enabled
        if (this.config.enableFXAA && !this.config.enableSSAA) {
            this.composer.addPass(this.fxaaPass);
        }

        // Add film grain
        if (this.config.enableFilmGrain) {
            this.composer.addPass(this.filmPass);
        }
    }

    public setQuality(quality: 'low' | 'medium' | 'high' | 'ultra'): void {
        switch (quality) {
            case 'low':
                this.config.enableBloom = false;
                this.config.enableSSAA = false;
                this.config.enableFXAA = true;
                this.config.enableColorCorrection = false;
                this.config.enableFilmGrain = false;
                break;
            case 'medium':
                this.config.enableBloom = true;
                this.config.enableSSAA = false;
                this.config.enableFXAA = true;
                this.config.enableColorCorrection = true;
                this.config.enableFilmGrain = false;
                this.bloomStrength = 0.2;
                break;
            case 'high':
                this.config.enableBloom = true;
                this.config.enableSSAA = true;
                this.config.enableFXAA = false;
                this.config.enableColorCorrection = true;
                this.config.enableFilmGrain = true;
                this.bloomStrength = 0.3;
                break;
            case 'ultra':
                this.config.enableBloom = true;
                this.config.enableSSAA = true;
                this.config.enableFXAA = false;
                this.config.enableColorCorrection = true;
                this.config.enableFilmGrain = true;
                this.bloomStrength = 0.4;
                this.ssaaPass.sampleLevel = 4;
                break;
        }
        
        this.updateComposerPasses();
    }

    public setSelectedObjects(objects: THREE.Object3D[]): void {
        this.outlinePass.selectedObjects = objects;
    }

    public updateFXAAResolution(): void {
        const pixelRatio = this.renderer.getPixelRatio();
        this.fxaaPass.material.uniforms['resolution'].value.x = 1 / (window.innerWidth * pixelRatio);
        this.fxaaPass.material.uniforms['resolution'].value.y = 1 / (window.innerHeight * pixelRatio);
    }

    public onWindowResize(): void {
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        this.composer.setSize(width, height);
        this.bloomComposer.setSize(width, height);
        this.outlinePass.setSize(width, height);
        this.updateFXAAResolution();
    }

    public render(): void {
        if (this.config.enableBloom) {
            // Render bloom pass
            this.scene.traverse((obj) => {
                if ((obj as any).isBlooming) return;
                if (obj instanceof THREE.Mesh && obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(mat => {
                            if ((mat as any).uniforms?.darkenBloom) {
                                (mat as any).uniforms.darkenBloom.value = true;
                            }
                        });
                    } else if ((obj.material as any).uniforms?.darkenBloom) {
                        (obj.material as any).uniforms.darkenBloom.value = true;
                    }
                }
            });
            
            this.bloomComposer.render();
            
            // Restore materials
            this.scene.traverse((obj) => {
                if ((obj as any).isBlooming) return;
                if (obj instanceof THREE.Mesh && obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(mat => {
                            if ((mat as any).uniforms?.darkenBloom) {
                                (mat as any).uniforms.darkenBloom.value = false;
                            }
                        });
                    } else if ((obj.material as any).uniforms?.darkenBloom) {
                        (obj.material as any).uniforms.darkenBloom.value = false;
                    }
                }
            });
        }
        
        // Render main composer
        this.composer.render();
    }

    public dispose(): void {
        // EffectComposer doesn't have dispose method in this version
        // Individual passes will be garbage collected
        this.composer.passes = [];
        this.bloomComposer.passes = [];
    }

    // Getters for accessing passes
    get bloom() { return this.bloomPass; }
    get outline() { return this.outlinePass; }
    get colorCorrection() { return this.colorCorrectionPass; }
    get film() { return this.filmPass; }
    get bloomStrength() { return this.config.bloomStrength; }
    
    set bloomStrength(value: number) {
        this.config.bloomStrength = value;
        this.bloomPass.strength = value;
        this.finalPass.uniforms['bloomStrength'].value = value;
    }
}
