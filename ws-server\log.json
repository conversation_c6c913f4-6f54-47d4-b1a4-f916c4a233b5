[{"error": {"error": {"status": 502, "message": "Bad gateway."}}, "location": "192 index.js"}, {"error": {"device": {"id": "0c8f4f6b4c1432d4608f02ab654f26a6cbb19468", "is_active": true, "is_private_session": false, "is_restricted": false, "name": "SM-G988B", "type": "Smartphone", "volume_percent": 100}, "shuffle_state": false, "repeat_state": "off", "timestamp": 0, "context": null, "progress_ms": 0, "item": null, "currently_playing_type": "unknown", "actions": {"disallows": {"resuming": true, "seeking": true, "skipping_prev": true, "skipping_next": true, "toggling_repeat_context": true, "toggling_shuffle": true}}, "is_playing": true}, "location": "192 index.js"}, {"error": {"error": {"status": 502, "message": "Bad gateway."}}, "location": "192 index.js"}, {"error": {"error": {"status": 502, "message": "Bad gateway."}}, "location": "192 index.js"}, {"error": {"error": {"status": 502, "message": "Bad gateway."}}, "location": "192 index.js"}, {"error": {"error": {"status": 502, "message": "Bad gateway."}}, "location": "192 index.js"}, {"error": {"error": {"status": 502, "message": "Bad gateway."}}, "location": "192 index.js"}, {"error": {"error": {"status": 503, "message": "Service unavailable"}}, "location": "192 index.js"}, {"error": {"error": {"status": 503, "message": "Service unavailable"}}, "location": "192 index.js"}, {"error": {"error": {"status": 502, "message": "Bad gateway."}}, "location": "192 index.js"}, {"error": {}, "location": "207 index.js"}]