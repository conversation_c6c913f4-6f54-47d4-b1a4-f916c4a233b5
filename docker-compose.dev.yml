version: '3'
services:
  socketio:
    build: .
    # build: 
    #   context: ./dockerfiles
    #   dockerfile: Dockerfile
    ports:
      - "2000:2000"
      - "2001:2001"
    command: sh /usr/src/app/dev.sh
    deploy:
      resources:
        limits:
          memory: 512M
    volumes:
      - "./ws-server:/usr/src/app"
      # - "./refreshtoken.spotify.txt:/usr/src/app/refreshtoken.spotify.txt"
      - "../ssl/main:/etc/letsencrypt"
      - "../ssl/lib:/var/lib/letsencrypt"
